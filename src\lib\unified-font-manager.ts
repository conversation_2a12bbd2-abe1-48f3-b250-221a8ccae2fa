'use client';

import { PDFDocument, PDFFont } from 'pdf-lib';
import { FONT_MAPPING, getPdfFontKey } from './fonts';

/**
 * 统一字体管理器
 * 解决预览与PDF字体渲染不一致的问题
 */

export interface FontLoadResult {
  success: boolean;
  font?: PDFFont;
  fontData?: ArrayBuffer;
  error?: string;
  fallbackUsed?: boolean;
}

export interface FontMetrics {
  family: string;
  weight: number;
  size: number;
  width: number;
  height: number;
  baseline: number;
}

export class UnifiedFontManager {
  private static instance: UnifiedFontManager;
  private fontCache = new Map<string, ArrayBuffer>();
  private pdfFontCache = new Map<string, PDFFont>();
  private metricsCache = new Map<string, FontMetrics>();

  private constructor() {}

  static getInstance(): UnifiedFontManager {
    if (!UnifiedFontManager.instance) {
      UnifiedFontManager.instance = new UnifiedFontManager();
    }
    return UnifiedFontManager.instance;
  }

  /**
   * 改进的字体加载方法 - 使用多种策略确保成功
   */
  async loadFont(
    pdfDoc: PDFDocument,
    fontFamily: string,
    weight: number = 400
  ): Promise<FontLoadResult> {
    const cacheKey = `${fontFamily}-${weight}`;
    
    console.log(`🔤 UnifiedFontManager: 开始加载字体 ${cacheKey}`);

    // 检查PDF字体缓存
    if (this.pdfFontCache.has(cacheKey)) {
      console.log(`📦 从PDF字体缓存加载: ${cacheKey}`);
      return {
        success: true,
        font: this.pdfFontCache.get(cacheKey)!
      };
    }

    try {
      // 策略1: 尝试从Google Fonts加载
      const googleFontResult = await this.loadFromGoogleFonts(fontFamily, weight);
      if (googleFontResult.success && googleFontResult.fontData) {
        const font = await pdfDoc.embedFont(googleFontResult.fontData);
        this.pdfFontCache.set(cacheKey, font);
        console.log(`✅ Google Fonts加载成功: ${cacheKey}`);
        return { success: true, font };
      }

      // 策略2: 尝试从本地字体加载
      const localFontResult = await this.loadFromLocalFonts(pdfDoc, fontFamily, weight);
      if (localFontResult.success) {
        console.log(`✅ 本地字体加载成功: ${cacheKey}`);
        return localFontResult;
      }

      // 策略3: 使用后备字体
      const fallbackResult = await this.loadFallbackFont(pdfDoc, fontFamily);
      console.log(`⚠️ 使用后备字体: ${cacheKey} -> ${fallbackResult.font?.name}`);
      return { ...fallbackResult, fallbackUsed: true };

    } catch (error) {
      console.error(`❌ 字体加载失败: ${cacheKey}`, error);
      
      // 最后的后备方案
      const standardFont = await this.loadStandardFont(pdfDoc, fontFamily);
      return { ...standardFont, fallbackUsed: true };
    }
  }

  /**
   * 从Google Fonts加载字体 - 改进版本
   */
  private async loadFromGoogleFonts(
    fontFamily: string,
    weight: number
  ): Promise<{ success: boolean; fontData?: ArrayBuffer; error?: string }> {
    const cacheKey = `${fontFamily}-${weight}`;
    
    // 检查缓存
    if (this.fontCache.has(cacheKey)) {
      return {
        success: true,
        fontData: this.fontCache.get(cacheKey)!
      };
    }

    try {
      // 使用多个User-Agent策略
      const userAgents = [
        'Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0)', // TTF
        'Mozilla/5.0 (Windows NT 6.1; WOW64; rv:27.0) Gecko/20100101 Firefox/27.0', // TTF
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_9_2) AppleWebKit/537.36' // WOFF
      ];

      for (const userAgent of userAgents) {
        try {
          const result = await this.fetchFontWithUserAgent(fontFamily, weight, userAgent);
          if (result.success && result.fontData) {
            // 验证字体数据
            if (this.validateFontData(result.fontData)) {
              this.fontCache.set(cacheKey, result.fontData);
              console.log(`✅ Google Fonts加载成功 (${userAgent.slice(0, 20)}...): ${cacheKey}`);
              return result;
            }
          }
        } catch (error) {
          console.warn(`⚠️ User-Agent策略失败: ${userAgent.slice(0, 20)}...`, error);
          continue;
        }
      }

      return { success: false, error: '所有Google Fonts策略都失败' };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  /**
   * 使用特定User-Agent获取字体
   */
  private async fetchFontWithUserAgent(
    fontFamily: string,
    weight: number,
    userAgent: string
  ): Promise<{ success: boolean; fontData?: ArrayBuffer; error?: string }> {
    // 构建Google Fonts URL
    const fontUrl = this.buildGoogleFontsUrl(fontFamily, weight);
    
    // 获取CSS
    const cssResponse = await fetch(fontUrl, {
      headers: { 'User-Agent': userAgent }
    });
    
    if (!cssResponse.ok) {
      throw new Error(`CSS获取失败: ${cssResponse.status}`);
    }
    
    const cssText = await cssResponse.text();
    
    // 提取字体文件URL
    const fontFileUrl = this.extractFontUrl(cssText);
    if (!fontFileUrl) {
      throw new Error('无法从CSS中提取字体URL');
    }
    
    // 下载字体文件
    const fontResponse = await fetch(fontFileUrl, {
      headers: { 'User-Agent': userAgent }
    });
    
    if (!fontResponse.ok) {
      throw new Error(`字体文件下载失败: ${fontResponse.status}`);
    }
    
    const fontData = await fontResponse.arrayBuffer();
    
    return { success: true, fontData };
  }

  /**
   * 构建Google Fonts URL
   */
  private buildGoogleFontsUrl(fontFamily: string, weight: number): string {
    const encodedFamily = encodeURIComponent(fontFamily);
    return `https://fonts.googleapis.com/css2?family=${encodedFamily}:wght@${weight}&display=swap`;
  }

  /**
   * 从CSS中提取字体文件URL
   */
  private extractFontUrl(cssText: string): string | null {
    // 匹配 url() 中的字体文件URL
    const urlMatch = cssText.match(/url\(([^)]+)\)/);
    if (urlMatch && urlMatch[1]) {
      return urlMatch[1].replace(/['"]/g, ''); // 移除引号
    }
    return null;
  }

  /**
   * 验证字体数据
   */
  private validateFontData(fontData: ArrayBuffer): boolean {
    if (!fontData || fontData.byteLength === 0) {
      return false;
    }

    const view = new Uint8Array(fontData);
    
    // 检查TTF/OTF魔数
    const ttfMagic = [0x00, 0x01, 0x00, 0x00]; // TTF
    const otfMagic = [0x4F, 0x54, 0x54, 0x4F]; // OTF 'OTTO'
    const woffMagic = [0x77, 0x4F, 0x46, 0x46]; // WOFF 'wOFF'
    
    const header = Array.from(view.slice(0, 4));
    
    return (
      this.arraysEqual(header, ttfMagic) ||
      this.arraysEqual(header, otfMagic) ||
      this.arraysEqual(header, woffMagic)
    );
  }

  private arraysEqual(a: number[], b: number[]): boolean {
    return a.length === b.length && a.every((val, i) => val === b[i]);
  }

  /**
   * 从本地字体加载（预留接口）
   */
  private async loadFromLocalFonts(
    pdfDoc: PDFDocument,
    fontFamily: string,
    weight: number
  ): Promise<FontLoadResult> {
    // 这里可以实现从本地字体文件加载的逻辑
    // 目前返回失败，让系统使用其他策略
    return { success: false, error: '本地字体加载未实现' };
  }

  /**
   * 加载后备字体
   */
  private async loadFallbackFont(
    pdfDoc: PDFDocument,
    fontFamily: string
  ): Promise<FontLoadResult> {
    const mapping = FONT_MAPPING[fontFamily as keyof typeof FONT_MAPPING];

    if (mapping?.fallback) {
      // 尝试加载映射的后备字体
      const fallbackFamily = this.getFallbackFontFamily(mapping.fallback);
      if (fallbackFamily !== fontFamily) {
        return this.loadFont(pdfDoc, fallbackFamily, 400);
      }
    }

    // 使用标准字体作为最后的后备
    return this.loadStandardFont(pdfDoc, fontFamily);
  }

  /**
   * 加载标准字体
   */
  private async loadStandardFont(
    pdfDoc: PDFDocument,
    fontFamily: string
  ): Promise<FontLoadResult> {
    try {
      // 根据字体类型选择合适的标准字体
      let standardFont: PDFFont;

      if (fontFamily.toLowerCase().includes('script') ||
          fontFamily.toLowerCase().includes('vibes') ||
          fontFamily.toLowerCase().includes('dancing')) {
        // 手写体类字体使用Times Roman
        standardFont = pdfDoc.embedStandardFont('Times-Roman');
      } else if (fontFamily.toLowerCase().includes('serif') ||
                 fontFamily.toLowerCase().includes('playfair') ||
                 fontFamily.toLowerCase().includes('crimson')) {
        // 衬线字体使用Times Roman
        standardFont = pdfDoc.embedStandardFont('Times-Roman');
      } else {
        // 无衬线字体使用Helvetica
        standardFont = pdfDoc.embedStandardFont('Helvetica');
      }

      console.log(`📝 使用标准字体: ${fontFamily} -> ${standardFont.name}`);
      return { success: true, font: standardFont };
    } catch (error) {
      console.error('❌ 标准字体加载失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 获取后备字体族名
   */
  private getFallbackFontFamily(fallback: string): string {
    const fallbackMap: Record<string, string> = {
      'cursive': 'Times-Roman',
      'serif': 'Times-Roman',
      'sans-serif': 'Helvetica',
      'monospace': 'Courier'
    };

    return fallbackMap[fallback] || 'Helvetica';
  }

  /**
   * 计算字体度量信息
   */
  calculateFontMetrics(
    font: PDFFont,
    text: string,
    size: number
  ): FontMetrics {
    const cacheKey = `${font.name}-${size}-${text}`;

    if (this.metricsCache.has(cacheKey)) {
      return this.metricsCache.get(cacheKey)!;
    }

    const width = font.widthOfTextAtSize(text, size);
    const height = font.heightAtSize(size);
    const baseline = height * 0.8; // 估算基线位置

    const metrics: FontMetrics = {
      family: font.name || 'Unknown',
      weight: 400, // 默认权重
      size,
      width,
      height,
      baseline
    };

    this.metricsCache.set(cacheKey, metrics);
    return metrics;
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.fontCache.clear();
    this.pdfFontCache.clear();
    this.metricsCache.clear();
    console.log('🧹 字体缓存已清除');
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): {
    fontCacheSize: number;
    pdfFontCacheSize: number;
    metricsCacheSize: number;
  } {
    return {
      fontCacheSize: this.fontCache.size,
      pdfFontCacheSize: this.pdfFontCache.size,
      metricsCacheSize: this.metricsCache.size
    };
  }

  /**
   * 预加载常用字体
   */
  async preloadCommonFonts(pdfDoc: PDFDocument): Promise<void> {
    const commonFonts = [
      { family: 'Dancing Script', weight: 400 },
      { family: 'Playfair Display', weight: 400 },
      { family: 'Playfair Display', weight: 600 },
      { family: 'Inter', weight: 400 },
      { family: 'Crimson Text', weight: 400 },
      { family: 'Source Sans Pro', weight: 400 },
      { family: 'Great Vibes', weight: 400 }
    ];

    console.log('🚀 开始预加载常用字体...');

    const loadPromises = commonFonts.map(({ family, weight }) =>
      this.loadFont(pdfDoc, family, weight).catch(error => {
        console.warn(`⚠️ 预加载字体失败: ${family}-${weight}`, error);
        return { success: false, error: error.message };
      })
    );

    const results = await Promise.all(loadPromises);
    const successCount = results.filter(r => r.success).length;

    console.log(`✅ 预加载完成: ${successCount}/${commonFonts.length} 个字体加载成功`);
  }
}
