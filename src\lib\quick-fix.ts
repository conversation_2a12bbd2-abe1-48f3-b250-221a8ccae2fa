/**
 * 快速修复脚本
 * 解决Dancing Script字体在PDF中的显示问题
 */

import { FontConverter } from './font-converter';

/**
 * 快速修复Dancing Script字体问题
 */
export async function quickFixDancingScript(): Promise<boolean> {
  try {
    console.log('🔧 开始快速修复Dancing Script字体问题...');
    
    // 1. 预加载Dancing Script字体
    const result = await FontConverter.fetchTTFFont('Dancing Script', 400);
    
    if (result.success && result.fontData) {
      console.log('✅ Dancing Script字体TTF格式加载成功');
      console.log(`📊 字体文件大小: ${result.fontData.byteLength} bytes`);
      console.log(`📝 字体格式: ${result.format}`);
      
      // 2. 验证字体数据
      const isValid = result.fontData.byteLength > 0;
      if (isValid) {
        console.log('✅ 字体数据验证通过');
        return true;
      } else {
        console.error('❌ 字体数据验证失败');
        return false;
      }
    } else {
      console.error('❌ Dancing Script字体加载失败:', result.error);
      return false;
    }
  } catch (error) {
    console.error('❌ 快速修复过程中发生错误:', error);
    return false;
  }
}

/**
 * 测试字体渲染效果
 */
export async function testFontRendering(): Promise<void> {
  console.log('🧪 开始测试字体渲染效果...');
  
  try {
    // 测试所有关键字体
    const testFonts = [
      { family: 'Dancing Script', weight: 400 },
      { family: 'Playfair Display', weight: 600 },
      { family: 'Inter', weight: 400 }
    ];
    
    for (const font of testFonts) {
      const result = await FontConverter.fetchTTFFont(font.family, font.weight);
      
      if (result.success) {
        console.log(`✅ ${font.family}-${font.weight}: 加载成功 (${result.format})`);
      } else {
        console.log(`❌ ${font.family}-${font.weight}: 加载失败 - ${result.error}`);
      }
    }
  } catch (error) {
    console.error('❌ 字体渲染测试失败:', error);
  }
}

/**
 * 清理和重置字体缓存
 */
export function resetFontCache(): void {
  console.log('🗑️ 清理字体缓存...');
  FontConverter.clearCache();
  console.log('✅ 字体缓存已清理');
}

/**
 * 获取字体状态报告
 */
export function getFontStatusReport(): {
  cacheSize: number;
  cachedFonts: string[];
  recommendations: string[];
} {
  const cacheStatus = FontConverter.getCacheStatus();
  
  const recommendations = [];
  
  if (cacheStatus.size === 0) {
    recommendations.push('建议预加载关键字体以提高性能');
  }
  
  if (!cacheStatus.keys.some(key => key.includes('Dancing Script'))) {
    recommendations.push('Dancing Script字体未缓存，可能影响PDF生成');
  }
  
  return {
    cacheSize: cacheStatus.size,
    cachedFonts: cacheStatus.keys,
    recommendations
  };
}

/**
 * 一键修复所有字体问题
 */
export async function fixAllFontIssues(): Promise<{
  success: boolean;
  results: Array<{ font: string; success: boolean; error?: string }>;
}> {
  console.log('🔧 开始一键修复所有字体问题...');
  
  const results = [];
  let overallSuccess = true;
  
  // 预加载所有关键字体
  const criticalFonts = [
    { family: 'Dancing Script', weight: 400 },
    { family: 'Playfair Display', weight: 400 },
    { family: 'Playfair Display', weight: 600 },
    { family: 'Inter', weight: 400 },
    { family: 'Inter', weight: 600 },
    { family: 'Great Vibes', weight: 400 }
  ];
  
  for (const font of criticalFonts) {
    try {
      const result = await FontConverter.fetchTTFFont(font.family, font.weight);
      const fontKey = `${font.family}-${font.weight}`;
      
      if (result.success) {
        results.push({ font: fontKey, success: true });
        console.log(`✅ ${fontKey}: 修复成功`);
      } else {
        results.push({ font: fontKey, success: false, error: result.error });
        console.log(`❌ ${fontKey}: 修复失败 - ${result.error}`);
        overallSuccess = false;
      }
    } catch (error) {
      const fontKey = `${font.family}-${font.weight}`;
      results.push({ 
        font: fontKey, 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      });
      console.log(`❌ ${fontKey}: 修复异常 - ${error}`);
      overallSuccess = false;
    }
  }
  
  console.log(`🎉 一键修复完成! 总体状态: ${overallSuccess ? '成功' : '部分失败'}`);
  
  return {
    success: overallSuccess,
    results
  };
}

/**
 * 诊断当前字体问题
 */
export async function diagnoseFontIssues(): Promise<{
  issues: string[];
  recommendations: string[];
}> {
  console.log('🔍 开始诊断字体问题...');
  
  const issues = [];
  const recommendations = [];
  
  // 检查字体缓存状态
  const cacheStatus = FontConverter.getCacheStatus();
  
  if (cacheStatus.size === 0) {
    issues.push('字体缓存为空');
    recommendations.push('运行 fixAllFontIssues() 预加载字体');
  }
  
  // 检查关键字体
  const criticalFonts = ['Dancing Script-400', 'Playfair Display-600', 'Inter-400'];
  const missingFonts = criticalFonts.filter(font => !cacheStatus.keys.includes(font));
  
  if (missingFonts.length > 0) {
    issues.push(`缺失关键字体: ${missingFonts.join(', ')}`);
    recommendations.push('运行 quickFixDancingScript() 修复Dancing Script字体');
  }
  
  // 检查网络连接
  try {
    const testResult = await FontConverter.fetchTTFFont('Inter', 400);
    if (!testResult.success) {
      issues.push('网络连接问题，无法加载Google Fonts');
      recommendations.push('检查网络连接或使用本地字体文件');
    }
  } catch (error) {
    issues.push('字体加载器异常');
    recommendations.push('检查FontConverter类的实现');
  }
  
  console.log(`🔍 诊断完成: 发现 ${issues.length} 个问题`);
  
  return { issues, recommendations };
}

// 导出便捷函数
export const FontFix = {
  quick: quickFixDancingScript,
  test: testFontRendering,
  reset: resetFontCache,
  status: getFontStatusReport,
  fixAll: fixAllFontIssues,
  diagnose: diagnoseFontIssues
};
