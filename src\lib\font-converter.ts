'use client';

/**
 * 字体转换和优化工具
 * 解决pdf-lib与Google Fonts WOFF2格式的兼容性问题
 */

export interface FontConversionResult {
  success: boolean;
  fontData?: ArrayBuffer;
  error?: string;
  format?: string;
}

export class FontConverter {
  private static conversionCache = new Map<string, ArrayBuffer>();

  /**
   * 从Google Fonts API获取TTF格式字体
   * 使用特定的User-Agent来获取TTF而非WOFF2
   */
  static async fetchTTFFont(fontFamily: string, weight: number = 400): Promise<FontConversionResult> {
    const cacheKey = `${fontFamily}-${weight}-ttf`;
    
    // 检查缓存
    if (this.conversionCache.has(cacheKey)) {
      return {
        success: true,
        fontData: this.conversionCache.get(cacheKey)!,
        format: 'ttf'
      };
    }

    try {
      // 使用特定的User-Agent来获取TTF格式
      // 老版本的浏览器User-Agent会让Google Fonts返回TTF格式
      const userAgent = 'Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0)';
      
      // 构建Google Fonts CSS URL
      const fontUrl = this.buildGoogleFontsCSSUrl(fontFamily, weight);
      
      console.log(`🔄 获取字体CSS: ${fontFamily}-${weight}`);
      
      // 获取CSS文件
      const cssResponse = await fetch(fontUrl, {
        headers: { 'User-Agent': userAgent }
      });
      
      if (!cssResponse.ok) {
        throw new Error(`Failed to fetch CSS: ${cssResponse.status}`);
      }
      
      const cssText = await cssResponse.text();
      console.log(`📄 CSS内容获取成功，长度: ${cssText.length}`);
      
      // 从CSS中提取字体文件URL
      const fontFileUrl = this.extractFontUrlFromCSS(cssText);
      
      if (!fontFileUrl) {
        throw new Error('无法从CSS中提取字体文件URL');
      }
      
      console.log(`📥 下载字体文件: ${fontFileUrl}`);
      
      // 下载字体文件
      const fontResponse = await fetch(fontFileUrl, {
        headers: { 'User-Agent': userAgent }
      });
      
      if (!fontResponse.ok) {
        throw new Error(`Failed to fetch font file: ${fontResponse.status}`);
      }
      
      const fontData = await fontResponse.arrayBuffer();
      console.log(`✅ 字体文件下载成功: ${fontData.byteLength} bytes`);
      
      // 验证字体格式
      const format = this.detectFontFormat(fontData);
      console.log(`🔍 检测到字体格式: ${format}`);
      
      // 缓存字体数据
      this.conversionCache.set(cacheKey, fontData);
      
      return {
        success: true,
        fontData,
        format
      };
      
    } catch (error) {
      console.error(`❌ 字体转换失败 ${fontFamily}-${weight}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 构建Google Fonts CSS URL
   */
  private static buildGoogleFontsCSSUrl(fontFamily: string, weight: number): string {
    const encodedFamily = encodeURIComponent(fontFamily);
    return `https://fonts.googleapis.com/css?family=${encodedFamily}:${weight}&display=swap`;
  }

  /**
   * 从CSS中提取字体文件URL
   */
  private static extractFontUrlFromCSS(cssText: string): string | null {
    // 匹配 url() 中的字体文件URL
    const urlMatch = cssText.match(/url\(([^)]+)\)/);
    if (urlMatch && urlMatch[1]) {
      // 移除引号
      return urlMatch[1].replace(/['"]/g, '');
    }
    return null;
  }

  /**
   * 检测字体格式
   */
  private static detectFontFormat(fontData: ArrayBuffer): string {
    const view = new Uint8Array(fontData);
    
    // TTF格式检测 (以 0x00010000 开头)
    if (view[0] === 0x00 && view[1] === 0x01 && view[2] === 0x00 && view[3] === 0x00) {
      return 'ttf';
    }
    
    // OTF格式检测 (以 'OTTO' 开头)
    if (view[0] === 0x4F && view[1] === 0x54 && view[2] === 0x54 && view[3] === 0x4F) {
      return 'otf';
    }
    
    // WOFF格式检测 (以 'wOFF' 开头)
    if (view[0] === 0x77 && view[1] === 0x4F && view[2] === 0x46 && view[3] === 0x46) {
      return 'woff';
    }
    
    // WOFF2格式检测 (以 'wOF2' 开头)
    if (view[0] === 0x77 && view[1] === 0x4F && view[2] === 0x46 && view[3] === 0x32) {
      return 'woff2';
    }
    
    return 'unknown';
  }

  /**
   * 批量转换字体
   */
  static async convertFonts(fontConfigs: Array<{ family: string; weight?: number }>): Promise<Map<string, ArrayBuffer>> {
    const results = new Map<string, ArrayBuffer>();
    
    console.log(`🔄 开始批量转换 ${fontConfigs.length} 个字体...`);
    
    for (const { family, weight = 400 } of fontConfigs) {
      const result = await this.fetchTTFFont(family, weight);
      
      if (result.success && result.fontData) {
        const key = `${family}-${weight}`;
        results.set(key, result.fontData);
        results.set(family, result.fontData); // 也用简单名称作为键
        console.log(`✅ 字体转换成功: ${key} (${result.format})`);
      } else {
        console.warn(`❌ 字体转换失败: ${family}-${weight}`, result.error);
      }
    }
    
    console.log(`🎉 批量转换完成，成功: ${results.size / 2} 个字体`);
    return results;
  }

  /**
   * 预加载关键字体
   */
  static async preloadCriticalFonts(): Promise<Map<string, ArrayBuffer>> {
    const criticalFonts = [
      { family: 'Dancing Script', weight: 400 },
      { family: 'Playfair Display', weight: 400 },
      { family: 'Playfair Display', weight: 600 },
      { family: 'Inter', weight: 400 },
      { family: 'Inter', weight: 600 },
      { family: 'Great Vibes', weight: 400 }
    ];
    
    return await this.convertFonts(criticalFonts);
  }

  /**
   * 清除字体缓存
   */
  static clearCache(): void {
    this.conversionCache.clear();
    console.log('🗑️ 字体缓存已清除');
  }

  /**
   * 获取缓存状态
   */
  static getCacheStatus(): { size: number; keys: string[] } {
    return {
      size: this.conversionCache.size,
      keys: Array.from(this.conversionCache.keys())
    };
  }
}

/**
 * 字体验证工具
 */
export class FontValidator {
  /**
   * 验证字体数据的完整性
   */
  static validateFontData(fontData: ArrayBuffer): boolean {
    if (!fontData || fontData.byteLength === 0) {
      return false;
    }
    
    const view = new Uint8Array(fontData);
    
    // 基本的字体格式检查
    const format = FontConverter['detectFontFormat'](fontData);
    return format !== 'unknown';
  }

  /**
   * 测试字体渲染能力
   */
  static async testFontRendering(fontData: ArrayBuffer, testText: string = 'Test'): Promise<boolean> {
    try {
      // 这里可以添加更复杂的字体渲染测试
      // 目前只做基本的数据验证
      return this.validateFontData(fontData);
    } catch (error) {
      console.warn('字体渲染测试失败:', error);
      return false;
    }
  }
}
