'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, AlertCircle, Download } from 'lucide-react';

interface TestResult {
  category: string;
  test: string;
  status: 'pass' | 'fail' | 'warning';
  details: string;
  expectedValue?: string;
  actualValue?: string;
  timestamp: number;
}

interface ComprehensiveTestReportProps {
  debugInfo: string[];
  coordinateAnalysis: any;
  scaleFactor: number;
}

export default function ComprehensiveTestReport({ 
  debugInfo, 
  coordinateAnalysis, 
  scaleFactor 
}: ComprehensiveTestReportProps) {
  const [testResults, setTestResults] = useState<TestResult[]>([]);

  const runComprehensiveTests = () => {
    const results: TestResult[] = [];
    const timestamp = Date.now();

    // 字体加载测试
    const fontLoadingLogs = debugInfo.filter(log => log.includes('Dancing Script') || log.includes('字体'));
    const dancingScriptLoaded = fontLoadingLogs.some(log => log.includes('Dancing Script') && log.includes('✅'));
    
    results.push({
      category: '字体加载',
      test: 'Dancing Script字体加载',
      status: dancingScriptLoaded ? 'pass' : 'fail',
      details: dancingScriptLoaded ? 'Dancing Script字体成功加载' : 'Dancing Script字体加载失败',
      expectedValue: '字体成功嵌入',
      actualValue: dancingScriptLoaded ? '已嵌入' : '未嵌入',
      timestamp
    });

    // 字体映射测试
    const fontMappingLogs = debugInfo.filter(log => log.includes('字体映射') || log.includes('精确匹配'));
    const fontMappingSuccess = fontMappingLogs.some(log => log.includes('✅'));
    
    results.push({
      category: '字体映射',
      test: '字体匹配逻辑',
      status: fontMappingSuccess ? 'pass' : 'warning',
      details: fontMappingSuccess ? '字体匹配逻辑正常工作' : '字体匹配可能存在问题',
      expectedValue: '正确匹配Dancing Script',
      actualValue: fontMappingSuccess ? '匹配成功' : '匹配异常',
      timestamp
    });

    // 坐标系统测试
    if (coordinateAnalysis) {
      const scaleFactorTest = Math.abs(scaleFactor - 0.53) < 0.05;
      results.push({
        category: '坐标系统',
        test: '缩放因子合理性',
        status: scaleFactorTest ? 'pass' : 'warning',
        details: `当前缩放因子: ${scaleFactor}`,
        expectedValue: '0.48-0.58范围内',
        actualValue: scaleFactor.toString(),
        timestamp
      });

      // 坐标一致性测试
      const nameCoordinate = coordinateAnalysis.字段分析?.姓名;
      if (nameCoordinate) {
        const coordinateConsistency = nameCoordinate.模板坐标 && nameCoordinate.预览坐标 && nameCoordinate.PDF坐标;
        results.push({
          category: '坐标系统',
          test: '坐标数据完整性',
          status: coordinateConsistency ? 'pass' : 'fail',
          details: '所有字段的坐标数据完整',
          expectedValue: '模板、预览、PDF坐标都存在',
          actualValue: coordinateConsistency ? '完整' : '不完整',
          timestamp
        });
      }
    }

    // PDF生成测试
    const pdfGenerationLogs = debugInfo.filter(log => log.includes('PDF') && (log.includes('生成') || log.includes('完成')));
    const pdfGenerationSuccess = pdfGenerationLogs.some(log => log.includes('✅') || log.includes('成功'));
    
    results.push({
      category: 'PDF生成',
      test: 'PDF生成状态',
      status: pdfGenerationSuccess ? 'pass' : 'fail',
      details: pdfGenerationSuccess ? 'PDF生成成功' : 'PDF生成失败',
      expectedValue: '成功生成PDF文件',
      actualValue: pdfGenerationSuccess ? '生成成功' : '生成失败',
      timestamp
    });

    // 绿色横线检查
    const decorationLogs = debugInfo.filter(log => log.includes('装饰') || log.includes('横线'));
    const noGreenLine = !decorationLogs.some(log => log.includes('绿色') && log.includes('绘制'));
    
    results.push({
      category: '视觉效果',
      test: '绿色横线移除',
      status: noGreenLine ? 'pass' : 'fail',
      details: noGreenLine ? '未检测到绿色横线绘制' : '可能仍存在绿色横线',
      expectedValue: '无绿色横线',
      actualValue: noGreenLine ? '已移除' : '可能存在',
      timestamp
    });

    // 调试信息完整性
    const debugInfoComplete = debugInfo.length > 10;
    results.push({
      category: '调试系统',
      test: '调试信息完整性',
      status: debugInfoComplete ? 'pass' : 'warning',
      details: `收集到${debugInfo.length}条调试信息`,
      expectedValue: '充足的调试信息',
      actualValue: `${debugInfo.length}条`,
      timestamp
    });

    setTestResults(results);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pass':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'fail':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusBadge = (status: TestResult['status']) => {
    switch (status) {
      case 'pass':
        return <Badge className="bg-green-100 text-green-800">通过</Badge>;
      case 'fail':
        return <Badge className="bg-red-100 text-red-800">失败</Badge>;
      case 'warning':
        return <Badge className="bg-yellow-100 text-yellow-800">警告</Badge>;
    }
  };

  const exportReport = () => {
    const report = {
      title: '证书生成器综合测试报告',
      timestamp: new Date().toISOString(),
      summary: {
        总测试数: testResults.length,
        通过: testResults.filter(r => r.status === 'pass').length,
        失败: testResults.filter(r => r.status === 'fail').length,
        警告: testResults.filter(r => r.status === 'warning').length
      },
      testResults,
      debugInfo,
      coordinateAnalysis,
      scaleFactor
    };

    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `certificate-test-report-${Date.now()}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const passCount = testResults.filter(r => r.status === 'pass').length;
  const failCount = testResults.filter(r => r.status === 'fail').length;
  const warningCount = testResults.filter(r => r.status === 'warning').length;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>📊 综合测试报告</span>
          <div className="flex gap-2">
            <Button onClick={runComprehensiveTests} size="sm">
              运行测试
            </Button>
            {testResults.length > 0 && (
              <Button onClick={exportReport} size="sm" variant="outline">
                <Download className="h-4 w-4 mr-2" />
                导出报告
              </Button>
            )}
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {testResults.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            点击"运行测试"开始综合测试分析
          </div>
        ) : (
          <div className="space-y-4">
            {/* 测试摘要 */}
            <div className="grid grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold">{testResults.length}</div>
                <div className="text-sm text-gray-600">总测试数</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{passCount}</div>
                <div className="text-sm text-gray-600">通过</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{failCount}</div>
                <div className="text-sm text-gray-600">失败</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">{warningCount}</div>
                <div className="text-sm text-gray-600">警告</div>
              </div>
            </div>

            {/* 测试结果详情 */}
            <div className="space-y-3">
              {Object.entries(
                testResults.reduce((acc, result) => {
                  if (!acc[result.category]) acc[result.category] = [];
                  acc[result.category].push(result);
                  return acc;
                }, {} as Record<string, TestResult[]>)
              ).map(([category, results]) => (
                <div key={category} className="border rounded-lg p-4">
                  <h4 className="font-medium mb-3">{category}</h4>
                  <div className="space-y-2">
                    {results.map((result, index) => (
                      <div key={index} className="flex items-start gap-3 p-3 bg-gray-50 rounded">
                        {getStatusIcon(result.status)}
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-medium">{result.test}</span>
                            {getStatusBadge(result.status)}
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{result.details}</p>
                          {result.expectedValue && result.actualValue && (
                            <div className="text-xs space-y-1">
                              <div>期望值: <span className="font-mono">{result.expectedValue}</span></div>
                              <div>实际值: <span className="font-mono">{result.actualValue}</span></div>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
