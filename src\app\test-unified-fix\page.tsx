'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { CERTIFICATE_TEMPLATES } from '@/lib/certificate-templates';
import CertificatePreview from '@/components/certificate/CertificatePreview';
import { PDFGenerator } from '@/lib/pdf-generator';
import { quickTestFontConsistency, quickTestDancingScript } from '@/lib/font-consistency-test';
import { runQuickTest, generateTestReport } from '@/lib/test-font-fix';
import type { CertificateData } from '@/types/certificate';
import type { ConsistencyTestResult } from '@/lib/font-consistency-test';
import type { QuickTestResult } from '@/lib/test-font-fix';

export default function TestUnifiedFixPage() {
  const { toast } = useToast();
  const [testResult, setTestResult] = useState<ConsistencyTestResult | null>(null);
  const [quickTestResult, setQuickTestResult] = useState<QuickTestResult | null>(null);
  const [isTestingFonts, setIsTestingFonts] = useState(false);
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const [dancingScriptStatus, setDancingScriptStatus] = useState<'unknown' | 'success' | 'failed'>('unknown');

  // 获取completion-template-1
  const template = CERTIFICATE_TEMPLATES.find(t => t.id === 'completion-template-1');
  
  const [formData] = useState<CertificateData>({
    templateId: 'completion-template-1',
    recipientName: '张三',
    date: '2024年1月15日',
    signature: '李老师',
    details: '恭喜您成功完成了《Web开发基础课程》的学习，表现优异，特此颁发此证书以资鼓励。本课程涵盖了HTML、CSS、JavaScript等核心技术。',
  });

  // 快速测试字体修复
  const runQuickTestFix = async () => {
    setIsTestingFonts(true);
    try {
      const result = await runQuickTest();
      setQuickTestResult(result);

      // 更新Dancing Script状态
      if (result.dancingScriptTest.success && !result.dancingScriptTest.fallbackUsed) {
        setDancingScriptStatus('success');
      } else {
        setDancingScriptStatus('failed');
      }

      toast({
        title: "快速测试完成",
        description: `总体状态: ${result.overallStatus}`,
        variant: result.overallStatus === 'success' ? "default" : "destructive",
      });
    } catch (error) {
      setDancingScriptStatus('failed');
      toast({
        title: "测试出错",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive",
      });
    } finally {
      setIsTestingFonts(false);
    }
  };

  // 测试Dancing Script字体
  const testDancingScript = async () => {
    try {
      const success = await quickTestDancingScript();
      setDancingScriptStatus(success ? 'success' : 'failed');

      toast({
        title: success ? "Dancing Script测试成功" : "Dancing Script测试失败",
        description: success ? "字体加载正常，未使用后备字体" : "字体加载失败或使用了后备字体",
        variant: success ? "default" : "destructive",
      });
    } catch (error) {
      setDancingScriptStatus('failed');
      toast({
        title: "测试出错",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive",
      });
    }
  };

  // 运行完整的字体一致性测试
  const runFullTest = async () => {
    setIsTestingFonts(true);
    try {
      const result = await quickTestFontConsistency();
      setTestResult(result);
      
      toast({
        title: "测试完成",
        description: `总体评分: ${result.overallScore.toFixed(1)}/100`,
        variant: result.overallScore >= 80 ? "default" : "destructive",
      });
    } catch (error) {
      toast({
        title: "测试失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive",
      });
    } finally {
      setIsTestingFonts(false);
    }
  };

  // 生成PDF进行对比
  const generateTestPDF = async () => {
    if (!template) return;
    
    setIsGeneratingPDF(true);
    try {
      const generator = new PDFGenerator(template, formData);
      const pdfBytes = await generator.generate();
      
      // 下载PDF
      const blob = new Blob([pdfBytes], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'unified-fix-test-certificate.pdf';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      toast({
        title: "PDF生成成功",
        description: "请对比预览与PDF中的字体显示效果",
      });
    } catch (error) {
      toast({
        title: "PDF生成失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  // 页面加载时自动运行快速测试
  useEffect(() => {
    runQuickTestFix();
  }, []);

  if (!template) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardContent className="pt-6">
            <p className="text-red-500">错误：找不到completion-template-1模板</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">统一字体管理器验证测试</h1>
        <p className="text-muted-foreground">
          验证统一字体管理器和渲染一致性管理器的修复效果
        </p>
      </div>

      {/* 快速状态检查 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            字体状态检查
            <Badge variant={dancingScriptStatus === 'success' ? 'default' : 
                           dancingScriptStatus === 'failed' ? 'destructive' : 'secondary'}>
              {dancingScriptStatus === 'success' ? '正常' : 
               dancingScriptStatus === 'failed' ? '异常' : '检查中...'}
            </Badge>
          </CardTitle>
          <CardDescription>
            Dancing Script字体加载状态和基本功能检查
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 flex-wrap">
            <Button onClick={runQuickTestFix} disabled={isTestingFonts} variant="default">
              {isTestingFonts ? '测试中...' : '快速测试'}
            </Button>
            <Button onClick={testDancingScript} variant="outline">
              单独测试Dancing Script
            </Button>
            <Button onClick={runFullTest} disabled={isTestingFonts} variant="outline">
              {isTestingFonts ? '测试中...' : '完整一致性测试'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 快速测试结果 */}
      {quickTestResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              快速测试结果
              <Badge variant={quickTestResult.overallStatus === 'success' ? 'default' :
                             quickTestResult.overallStatus === 'partial' ? 'secondary' : 'destructive'}>
                {quickTestResult.overallStatus === 'success' ? '✅ 成功' :
                 quickTestResult.overallStatus === 'partial' ? '⚠️ 部分' : '❌ 失败'}
              </Badge>
            </CardTitle>
            <CardDescription>
              核心功能快速验证结果
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold mb-2">Dancing Script字体</h4>
                <div className="space-y-1 text-sm">
                  <div className="flex items-center gap-2">
                    <Badge variant={quickTestResult.dancingScriptTest.success ? 'default' : 'destructive'}>
                      {quickTestResult.dancingScriptTest.success ? '✅' : '❌'}
                    </Badge>
                    <span>加载状态</span>
                  </div>
                  {quickTestResult.dancingScriptTest.fontName && (
                    <div className="text-muted-foreground">
                      字体: {quickTestResult.dancingScriptTest.fontName}
                    </div>
                  )}
                  {quickTestResult.dancingScriptTest.fallbackUsed && (
                    <Badge variant="secondary">使用后备字体</Badge>
                  )}
                </div>
              </div>

              <div>
                <h4 className="font-semibold mb-2">渲染一致性</h4>
                <div className="space-y-1 text-sm">
                  <div className="flex items-center gap-2">
                    <Badge variant={quickTestResult.renderingConsistency.configValid ? 'default' : 'destructive'}>
                      {quickTestResult.renderingConsistency.configValid ? '✅' : '❌'}
                    </Badge>
                    <span>配置有效性</span>
                  </div>
                  <div className="text-muted-foreground">
                    缩放因子: {quickTestResult.renderingConsistency.scaleFactor.toFixed(3)}
                  </div>
                </div>
              </div>
            </div>

            {quickTestResult.recommendations.length > 0 && (
              <>
                <Separator />
                <div>
                  <h4 className="font-semibold mb-2 text-blue-600">改进建议</h4>
                  <ul className="space-y-1 text-sm">
                    {quickTestResult.recommendations.map((rec, index) => (
                      <li key={index} className="text-blue-600">• {rec}</li>
                    ))}
                  </ul>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      )}

      {/* 详细测试结果 */}
      {testResult && (
        <Card>
          <CardHeader>
            <CardTitle>详细一致性测试结果</CardTitle>
            <CardDescription>
              总体评分: {testResult.overallScore.toFixed(1)}/100
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-semibold mb-2">渲染一致性</h4>
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>位置精度: {testResult.renderingConsistency.positionAccuracy}%</div>
                <div>尺寸精度: {testResult.renderingConsistency.sizeAccuracy}%</div>
                <div>字体匹配: {testResult.renderingConsistency.fontMatching.toFixed(1)}%</div>
              </div>
            </div>
            
            <Separator />
            
            <div>
              <h4 className="font-semibold mb-2">字体测试结果</h4>
              <div className="space-y-1 text-sm">
                {testResult.fontTests.map((test, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <Badge variant={test.loadSuccess ? 'default' : 'destructive'}>
                      {test.loadSuccess ? '✅' : '❌'}
                    </Badge>
                    <span>{test.fontFamily}</span>
                    {test.fallbackUsed && <Badge variant="secondary">后备字体</Badge>}
                    {test.fontName && <span className="text-muted-foreground">({test.fontName})</span>}
                  </div>
                ))}
              </div>
            </div>

            {testResult.issues.length > 0 && (
              <>
                <Separator />
                <div>
                  <h4 className="font-semibold mb-2 text-red-600">发现的问题</h4>
                  <ul className="space-y-1 text-sm">
                    {testResult.issues.map((issue, index) => (
                      <li key={index} className="text-red-600">• {issue}</li>
                    ))}
                  </ul>
                </div>
              </>
            )}

            {testResult.recommendations.length > 0 && (
              <>
                <Separator />
                <div>
                  <h4 className="font-semibold mb-2 text-blue-600">改进建议</h4>
                  <ul className="space-y-1 text-sm">
                    {testResult.recommendations.map((rec, index) => (
                      <li key={index} className="text-blue-600">• {rec}</li>
                    ))}
                  </ul>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      )}

      {/* 预览与PDF对比 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>预览效果</CardTitle>
            <CardDescription>
              使用改进的渲染一致性管理器
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="border rounded-lg p-4 bg-gray-50">
              <CertificatePreview
                template={template}
                formData={formData}
                showCoordinates={false}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>PDF生成测试</CardTitle>
            <CardDescription>
              生成PDF文件进行对比验证
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              onClick={generateTestPDF} 
              disabled={isGeneratingPDF}
              className="w-full"
            >
              {isGeneratingPDF ? '生成中...' : '生成测试PDF'}
            </Button>
            
            <div className="text-sm text-muted-foreground space-y-2">
              <p>• 点击按钮生成PDF文件</p>
              <p>• 对比预览与PDF中的字体显示</p>
              <p>• 检查Dancing Script是否正确显示为手写体</p>
              <p>• 验证文字位置和大小是否一致</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 技术说明 */}
      <Card>
        <CardHeader>
          <CardTitle>技术改进说明</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 text-sm">
          <div>
            <h4 className="font-semibold">1. 统一字体管理器 (UnifiedFontManager)</h4>
            <p className="text-muted-foreground">
              • 使用多种User-Agent策略获取TTF格式字体<br/>
              • 实现字体验证和缓存机制<br/>
              • 提供智能后备字体选择
            </p>
          </div>
          
          <div>
            <h4 className="font-semibold">2. 渲染一致性管理器 (RenderingConsistencyManager)</h4>
            <p className="text-muted-foreground">
              • 统一预览和PDF的坐标计算<br/>
              • 精确的缩放因子计算<br/>
              • 一致的字体度量和样式应用
            </p>
          </div>
          
          <div>
            <h4 className="font-semibold">3. 改进的预览组件</h4>
            <p className="text-muted-foreground">
              • 使用与PDF相同的坐标系统<br/>
              • 应用一致的字体渲染属性<br/>
              • 精确的文本对齐和定位
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
