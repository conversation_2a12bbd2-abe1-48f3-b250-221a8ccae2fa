'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FontFix } from '@/lib/quick-fix';

export default function TestFontFixPage() {
  const [status, setStatus] = useState<string>('准备就绪');
  const [results, setResults] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  // 页面加载时自动诊断
  useEffect(() => {
    handleDiagnose();
  }, []);

  const handleQuickFix = async () => {
    setIsLoading(true);
    setStatus('正在快速修复Dancing Script字体...');
    
    try {
      const success = await FontFix.quick();
      setStatus(success ? '✅ Dancing Script字体修复成功!' : '❌ Dancing Script字体修复失败');
    } catch (error) {
      setStatus(`❌ 修复过程中发生错误: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFixAll = async () => {
    setIsLoading(true);
    setStatus('正在修复所有字体问题...');
    
    try {
      const result = await FontFix.fixAll();
      setResults(result);
      setStatus(result.success ? '✅ 所有字体修复成功!' : '⚠️ 部分字体修复失败');
    } catch (error) {
      setStatus(`❌ 修复过程中发生错误: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTest = async () => {
    setIsLoading(true);
    setStatus('正在测试字体渲染...');
    
    try {
      await FontFix.test();
      setStatus('✅ 字体渲染测试完成，请查看控制台');
    } catch (error) {
      setStatus(`❌ 测试过程中发生错误: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDiagnose = async () => {
    setIsLoading(true);
    setStatus('正在诊断字体问题...');
    
    try {
      const diagnosis = await FontFix.diagnose();
      setResults(diagnosis);
      setStatus('✅ 诊断完成');
    } catch (error) {
      setStatus(`❌ 诊断过程中发生错误: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = () => {
    FontFix.reset();
    setStatus('✅ 字体缓存已清理');
    setResults(null);
  };

  const getStatusReport = () => {
    const report = FontFix.status();
    setResults(report);
    setStatus('✅ 状态报告已生成');
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">字体修复测试工具</h1>
        <p className="text-gray-600">
          用于测试和修复证书生成器中的字体显示问题，特别是Dancing Script字体在PDF中的渲染问题。
        </p>
      </div>

      {/* 状态显示 */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>当前状态</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="p-4 bg-gray-50 rounded-lg">
            <p className="font-mono text-sm">{status}</p>
          </div>
        </CardContent>
      </Card>

      {/* 操作按钮 */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>修复操作</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <Button 
              onClick={handleQuickFix} 
              disabled={isLoading}
              variant="default"
            >
              快速修复Dancing Script
            </Button>
            
            <Button 
              onClick={handleFixAll} 
              disabled={isLoading}
              variant="default"
            >
              修复所有字体
            </Button>
            
            <Button 
              onClick={handleTest} 
              disabled={isLoading}
              variant="outline"
            >
              测试字体渲染
            </Button>
            
            <Button 
              onClick={handleDiagnose} 
              disabled={isLoading}
              variant="outline"
            >
              诊断问题
            </Button>
            
            <Button 
              onClick={getStatusReport} 
              disabled={isLoading}
              variant="outline"
            >
              状态报告
            </Button>
            
            <Button 
              onClick={handleReset} 
              disabled={isLoading}
              variant="destructive"
            >
              清理缓存
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 结果显示 */}
      {results && (
        <Card>
          <CardHeader>
            <CardTitle>详细结果</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-50 p-4 rounded-lg overflow-auto text-sm">
              {JSON.stringify(results, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}

      {/* 使用说明 */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>使用说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 text-sm">
            <div>
              <h4 className="font-semibold">1. 快速修复Dancing Script</h4>
              <p className="text-gray-600">专门修复Dancing Script字体在PDF中的显示问题</p>
            </div>
            <div>
              <h4 className="font-semibold">2. 修复所有字体</h4>
              <p className="text-gray-600">预加载所有关键字体，确保PDF生成时字体可用</p>
            </div>
            <div>
              <h4 className="font-semibold">3. 测试字体渲染</h4>
              <p className="text-gray-600">测试各种字体的加载情况，结果显示在浏览器控制台</p>
            </div>
            <div>
              <h4 className="font-semibold">4. 诊断问题</h4>
              <p className="text-gray-600">自动检测当前字体配置中的问题并提供建议</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
