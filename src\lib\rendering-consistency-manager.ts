'use client';

import { PDFFont } from 'pdf-lib';
import { CertificateTemplate } from '@/types/certificate';
import { UnifiedFontManager, FontMetrics } from './unified-font-manager';

/**
 * 渲染一致性管理器
 * 确保预览与PDF渲染完全一致
 */

export interface RenderingConfig {
  scaleFactor: number;
  containerWidth: number;
  containerHeight: number;
  pdfWidth: number;
  pdfHeight: number;
}

export interface TextRenderingInfo {
  x: number;
  y: number;
  width: number;
  height: number;
  fontSize: number;
  fontFamily: string;
  fontWeight: number;
  color: string;
  align: 'left' | 'center' | 'right';
}

export interface ConsistentTextMetrics {
  previewStyle: React.CSSProperties;
  pdfCoordinates: {
    x: number;
    y: number;
    size: number;
  };
  fontMetrics: FontMetrics;
}

export class RenderingConsistencyManager {
  private fontManager: UnifiedFontManager;
  private config: RenderingConfig;

  constructor(template: CertificateTemplate) {
    this.fontManager = UnifiedFontManager.getInstance();
    this.config = this.calculateRenderingConfig(template);
  }

  /**
   * 计算渲染配置
   */
  private calculateRenderingConfig(template: CertificateTemplate): RenderingConfig {
    // PDF标准尺寸
    const pdfWidth = template.orientation === 'landscape' ? 842 : 595;
    const pdfHeight = template.orientation === 'landscape' ? 595 : 842;

    // 预览容器尺寸（基于实际CSS类）
    let containerWidth: number;
    let containerHeight: number;

    if (template.orientation === 'landscape') {
      // aspect-[4/3] w-full max-w-2xl
      containerWidth = 672; // max-w-2xl = 42rem = 672px
      containerHeight = containerWidth * (3/4); // aspect-[4/3]
    } else {
      // aspect-[3/4] w-full max-w-md  
      containerWidth = 448; // max-w-md = 28rem = 448px
      containerHeight = containerWidth * (4/3); // aspect-[3/4]
    }

    const scaleFactor = Math.min(
      containerWidth / pdfWidth,
      containerHeight / pdfHeight
    );

    return {
      scaleFactor,
      containerWidth,
      containerHeight,
      pdfWidth,
      pdfHeight
    };
  }

  /**
   * 计算一致的文本渲染信息
   */
  async calculateConsistentTextMetrics(
    font: PDFFont,
    textInfo: TextRenderingInfo,
    text: string
  ): Promise<ConsistentTextMetrics> {
    // 1. 计算字体度量
    const fontMetrics = this.fontManager.calculateFontMetrics(
      font,
      text,
      textInfo.fontSize
    );

    // 2. 计算PDF坐标
    const pdfCoordinates = this.calculatePdfCoordinates(textInfo, fontMetrics);

    // 3. 计算预览样式
    const previewStyle = this.calculatePreviewStyle(textInfo, fontMetrics);

    return {
      previewStyle,
      pdfCoordinates,
      fontMetrics
    };
  }

  /**
   * 计算PDF坐标
   */
  private calculatePdfCoordinates(
    textInfo: TextRenderingInfo,
    fontMetrics: FontMetrics
  ): { x: number; y: number; size: number } {
    let x = textInfo.x;

    // 处理文本对齐
    if (textInfo.align === 'center') {
      x = textInfo.x - fontMetrics.width / 2;
    } else if (textInfo.align === 'right') {
      x = textInfo.x - fontMetrics.width;
    }

    // PDF坐标系：Y轴从底部开始
    const y = this.config.pdfHeight - textInfo.y;

    return {
      x,
      y,
      size: textInfo.fontSize
    };
  }

  /**
   * 计算预览样式
   */
  private calculatePreviewStyle(
    textInfo: TextRenderingInfo,
    fontMetrics: FontMetrics
  ): React.CSSProperties {
    // 转换PDF坐标到预览坐标
    const previewX = textInfo.x * this.config.scaleFactor;
    const previewY = (this.config.pdfHeight - textInfo.y - textInfo.height) * this.config.scaleFactor;
    
    // 缩放字体大小
    const scaledFontSize = textInfo.fontSize * this.config.scaleFactor;
    
    // 缩放尺寸
    const scaledWidth = textInfo.width * this.config.scaleFactor;
    const scaledHeight = textInfo.height * this.config.scaleFactor;

    let justifyContent: string;
    let alignItems: string = 'center';

    switch (textInfo.align) {
      case 'center':
        justifyContent = 'center';
        break;
      case 'right':
        justifyContent = 'flex-end';
        break;
      default:
        justifyContent = 'flex-start';
    }

    return {
      position: 'absolute',
      left: `${previewX}px`,
      top: `${previewY}px`,
      width: `${scaledWidth}px`,
      height: `${scaledHeight}px`,
      fontSize: `${scaledFontSize}px`,
      fontWeight: textInfo.fontWeight,
      color: textInfo.color,
      display: 'flex',
      justifyContent,
      alignItems,
      lineHeight: '1.0',
      textRendering: 'optimizeLegibility',
      WebkitFontSmoothing: 'antialiased',
      MozOsxFontSmoothing: 'grayscale',
      // 特殊字体的字间距调整
      letterSpacing: this.getLetterSpacing(textInfo.fontFamily)
    };
  }

  /**
   * 获取字体特定的字间距
   */
  private getLetterSpacing(fontFamily: string): string {
    if (fontFamily.toLowerCase().includes('dancing') ||
        fontFamily.toLowerCase().includes('great vibes')) {
      return '0.02em';
    }
    return 'normal';
  }

  /**
   * 验证渲染一致性
   */
  validateConsistency(
    previewMetrics: ConsistentTextMetrics,
    pdfMetrics: ConsistentTextMetrics
  ): {
    isConsistent: boolean;
    differences: string[];
    tolerance: number;
  } {
    const differences: string[] = [];
    const tolerance = 2; // 2px容差

    // 检查位置差异
    const xDiff = Math.abs(
      parseFloat(previewMetrics.previewStyle.left as string) - 
      (pdfMetrics.pdfCoordinates.x * this.config.scaleFactor)
    );
    
    const yDiff = Math.abs(
      parseFloat(previewMetrics.previewStyle.top as string) - 
      ((this.config.pdfHeight - pdfMetrics.pdfCoordinates.y) * this.config.scaleFactor)
    );

    if (xDiff > tolerance) {
      differences.push(`X坐标差异: ${xDiff.toFixed(2)}px`);
    }

    if (yDiff > tolerance) {
      differences.push(`Y坐标差异: ${yDiff.toFixed(2)}px`);
    }

    // 检查字体大小差异
    const sizeDiff = Math.abs(
      parseFloat(previewMetrics.previewStyle.fontSize as string) - 
      (pdfMetrics.pdfCoordinates.size * this.config.scaleFactor)
    );

    if (sizeDiff > tolerance) {
      differences.push(`字体大小差异: ${sizeDiff.toFixed(2)}px`);
    }

    return {
      isConsistent: differences.length === 0,
      differences,
      tolerance
    };
  }

  /**
   * 获取渲染配置信息
   */
  getRenderingConfig(): RenderingConfig {
    return { ...this.config };
  }

  /**
   * 调试信息
   */
  getDebugInfo(): {
    config: RenderingConfig;
    cacheStats: any;
  } {
    return {
      config: this.config,
      cacheStats: this.fontManager.getCacheStats()
    };
  }

  /**
   * 计算精确的文本边界框
   */
  calculateTextBounds(
    font: PDFFont,
    text: string,
    fontSize: number,
    x: number,
    y: number,
    align: 'left' | 'center' | 'right'
  ): {
    x: number;
    y: number;
    width: number;
    height: number;
  } {
    const width = font.widthOfTextAtSize(text, fontSize);
    const height = font.heightAtSize(fontSize);

    let adjustedX = x;
    if (align === 'center') {
      adjustedX = x - width / 2;
    } else if (align === 'right') {
      adjustedX = x - width;
    }

    return {
      x: adjustedX,
      y: y - height, // 调整为文本基线
      width,
      height
    };
  }
}
