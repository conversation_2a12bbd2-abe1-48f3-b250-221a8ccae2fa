'use client';

import { PDFDocument, PDFFont } from 'pdf-lib';
import { FONT_MAPPING, getPdfFontKey } from './fonts';
import { FontConverter, FontValidator } from './font-converter';

/**
 * 字体加载器类 - 优化版本
 * 负责从Google Fonts加载字体文件，解决WOFF2兼容性问题
 */
export class FontLoader {
  private static fontCache: Map<string, ArrayBuffer> = new Map();
  private static fontUrlCache: Map<string, string> = new Map();

  /**
   * 使用改进的字体转换器加载字体
   * 优先获取TTF格式以确保pdf-lib兼容性
   */
  private static async loadGoogleFont(fontFamily: string, weight: number = 400): Promise<ArrayBuffer | null> {
    // 使用统一的字体键名
    const normalizedFontFamily = getPdfFontKey(fontFamily);
    const cacheKey = `${normalizedFontFamily}-${weight}`;

    // 检查缓存
    if (this.fontCache.has(cacheKey)) {
      console.log(`📦 从缓存加载字体: ${cacheKey}`);
      return this.fontCache.get(cacheKey)!;
    }

    try {
      console.log(`🔄 开始加载字体: ${cacheKey}`);

      // 使用改进的字体转换器获取TTF格式字体
      const result = await FontConverter.fetchTTFFont(normalizedFontFamily, weight);

      if (!result.success || !result.fontData) {
        console.warn(`❌ 字体转换失败: ${cacheKey}`, result.error);
        return null;
      }

      // 验证字体数据
      if (!FontValidator.validateFontData(result.fontData)) {
        console.warn(`❌ 字体数据验证失败: ${cacheKey}`);
        return null;
      }

      console.log(`✅ 字体加载成功: ${cacheKey} (${result.format}, ${result.fontData.byteLength} bytes)`);

      // 缓存字体数据
      this.fontCache.set(cacheKey, result.fontData);

      return result.fontData;
    } catch (error) {
      console.warn(`❌ 字体加载异常: ${cacheKey}`, error);
      return null;
    }
  }

  /**
   * 批量加载字体 - 增强版本，包含详细的嵌入状态检查
   */
  static async loadFonts(
    pdfDoc: PDFDocument,
    fontConfigs: Array<{ family: string; weight?: number }>
  ): Promise<Map<string, PDFFont>> {
    const fonts = new Map<string, PDFFont>();
    console.log(`🔤 FontLoader开始批量加载${fontConfigs.length}个字体...`);

    for (const { family, weight = 400 } of fontConfigs) {
      const fontKey = `${family}-${weight}`;
      console.log(`📥 开始加载字体: ${fontKey}`);

      try {
        // 步骤1: 下载字体文件
        const fontBytes = await this.loadGoogleFont(family, weight);
        if (!fontBytes) {
          console.warn(`❌ 字体文件下载失败: ${fontKey}`);
          continue;
        }

        console.log(`✅ 字体文件下载成功: ${fontKey}, 大小: ${fontBytes.byteLength} bytes`);

        // 步骤2: 嵌入字体到PDF文档
        console.log(`🔧 开始嵌入字体到PDF: ${fontKey}`);
        const font = await pdfDoc.embedFont(fontBytes);

        // 步骤3: 验证字体对象
        if (font) {
          console.log(`✅ 字体嵌入成功: ${fontKey}`);
          console.log(`📊 字体对象信息:`, {
            name: font.name || 'Unknown',
            constructor: font.constructor.name,
            hasWidthOfTextAtSize: typeof font.widthOfTextAtSize === 'function',
            hasHeightAtSize: typeof font.heightAtSize === 'function'
          });

          // 测试字体功能
          try {
            const testWidth = font.widthOfTextAtSize('Test', 12);
            console.log(`🧪 字体功能测试成功: 'Test'@12px = ${testWidth}px`);
          } catch (testError) {
            console.warn(`⚠️ 字体功能测试失败: ${fontKey}`, testError);
          }

          // 存储字体映射
          fonts.set(fontKey, font);
          fonts.set(family, font); // 也用简单名称作为键
          console.log(`💾 字体映射已保存: [${fontKey}] 和 [${family}]`);

          // 特别标记Dancing Script字体
          if (family.toLowerCase().includes('dancing script')) {
            console.log(`💃 Dancing Script字体嵌入成功! 键名: ${fontKey}`);
          }
        } else {
          console.error(`❌ 字体嵌入返回null: ${fontKey}`);
        }
      } catch (error) {
        console.error(`💥 字体嵌入失败: ${fontKey}`, error);
        console.error(`错误详情:`, {
          message: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined
        });
      }
    }

    console.log(`🎉 字体加载完成! 成功加载${fonts.size / 2}个字体`);
    console.log(`📋 已加载字体列表:`, Array.from(fonts.keys()));

    return fonts;
  }

  /**
   * 清除字体缓存
   */
  static clearCache(): void {
    this.fontCache.clear();
  }

  /**
   * 获取缓存大小
   */
  static getCacheSize(): number {
    return this.fontCache.size;
  }
}
