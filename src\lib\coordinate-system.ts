/**
 * 统一坐标系统工具
 * 解决PDF坐标系(底部原点)与CSS坐标系(顶部原点)的转换问题
 */

export interface Coordinates {
  x: number;
  y: number;
}

export interface Dimensions {
  width: number;
  height: number;
}

export interface Rectangle extends Coordinates, Dimensions {}

export interface PageDimensions {
  width: number;
  height: number;
}

/**
 * 标准PDF页面尺寸 (A4)
 */
export const PDF_PAGE_SIZES = {
  A4_PORTRAIT: { width: 595, height: 842 },
  A4_LANDSCAPE: { width: 842, height: 595 }
} as const;

/**
 * 坐标系统转换器
 */
export class CoordinateSystem {
  private pageHeight: number;

  constructor(pageHeight: number) {
    this.pageHeight = pageHeight;
  }

  /**
   * 将PDF坐标系(底部原点)转换为CSS坐标系(顶部原点)
   * PDF: Y轴从底部开始，向上为正
   * CSS: Y轴从顶部开始，向下为正
   */
  pdfToPreview(pdfCoords: Coordinates, elementHeight: number = 0): Coordinates {
    return {
      x: pdfCoords.x,
      y: this.pageHeight - pdfCoords.y - elementHeight
    };
  }

  /**
   * 将CSS坐标系(顶部原点)转换为PDF坐标系(底部原点)
   */
  previewToPdf(previewCoords: Coordinates, elementHeight: number = 0): Coordinates {
    return {
      x: previewCoords.x,
      y: this.pageHeight - previewCoords.y - elementHeight
    };
  }

  /**
   * 转换矩形区域坐标
   */
  pdfRectToPreview(pdfRect: Rectangle): Rectangle {
    const previewCoords = this.pdfToPreview(
      { x: pdfRect.x, y: pdfRect.y },
      pdfRect.height
    );
    
    return {
      x: previewCoords.x,
      y: previewCoords.y,
      width: pdfRect.width,
      height: pdfRect.height
    };
  }

  /**
   * 转换矩形区域坐标（预览到PDF）
   */
  previewRectToPdf(previewRect: Rectangle): Rectangle {
    const pdfCoords = this.previewToPdf(
      { x: previewRect.x, y: previewRect.y },
      previewRect.height
    );
    
    return {
      x: pdfCoords.x,
      y: pdfCoords.y,
      width: previewRect.width,
      height: previewRect.height
    };
  }

  /**
   * 应用缩放因子到坐标
   */
  applyScale(coords: Coordinates, scaleFactor: number): Coordinates {
    return {
      x: coords.x * scaleFactor,
      y: coords.y * scaleFactor
    };
  }

  /**
   * 应用缩放因子到矩形
   */
  applyScaleToRect(rect: Rectangle, scaleFactor: number): Rectangle {
    return {
      x: rect.x * scaleFactor,
      y: rect.y * scaleFactor,
      width: rect.width * scaleFactor,
      height: rect.height * scaleFactor
    };
  }

  /**
   * 获取页面高度
   */
  getPageHeight(): number {
    return this.pageHeight;
  }

  /**
   * 验证坐标是否在页面范围内
   */
  isWithinBounds(coords: Coordinates, dimensions?: Dimensions): boolean {
    const x = coords.x;
    const y = coords.y;
    const width = dimensions?.width || 0;
    const height = dimensions?.height || 0;

    return (
      x >= 0 &&
      y >= 0 &&
      x + width <= this.pageHeight && // 注意：这里应该是页面宽度，但我们主要关心高度
      y + height <= this.pageHeight
    );
  }
}

/**
 * 创建坐标系统实例的工厂函数
 */
export function createCoordinateSystem(orientation: 'portrait' | 'landscape'): CoordinateSystem {
  const pageSize = orientation === 'landscape' 
    ? PDF_PAGE_SIZES.A4_LANDSCAPE 
    : PDF_PAGE_SIZES.A4_PORTRAIT;
  
  return new CoordinateSystem(pageSize.height);
}

/**
 * 缩放因子计算器
 */
export class ScaleCalculator {
  /**
   * 根据容器尺寸和PDF尺寸计算最佳缩放因子
   */
  static calculateOptimalScale(
    containerSize: Dimensions,
    pdfSize: Dimensions,
    padding: number = 20
  ): number {
    const availableWidth = containerSize.width - padding * 2;
    const availableHeight = containerSize.height - padding * 2;
    
    const scaleX = availableWidth / pdfSize.width;
    const scaleY = availableHeight / pdfSize.height;
    
    // 使用较小的缩放因子以确保内容完全可见
    return Math.min(scaleX, scaleY);
  }

  /**
   * 获取预设的缩放因子
   */
  static getPresetScale(orientation: 'portrait' | 'landscape'): number {
    // 这些是经过测试的最佳缩放因子
    return orientation === 'landscape' ? 0.5 : 0.53;
  }
}

/**
 * 布局计算器
 */
export class LayoutCalculator {
  private coordinateSystem: CoordinateSystem;
  private scaleFactor: number;

  constructor(coordinateSystem: CoordinateSystem, scaleFactor: number) {
    this.coordinateSystem = coordinateSystem;
    this.scaleFactor = scaleFactor;
  }

  /**
   * 计算元素在预览中的最终样式
   */
  calculatePreviewStyle(pdfLayout: Rectangle): {
    left: string;
    top: string;
    width: string;
    height: string;
  } {
    // 1. 转换PDF坐标到预览坐标
    const previewRect = this.coordinateSystem.pdfRectToPreview(pdfLayout);
    
    // 2. 应用缩放因子
    const scaledRect = this.coordinateSystem.applyScaleToRect(previewRect, this.scaleFactor);
    
    return {
      left: `${scaledRect.x}px`,
      top: `${scaledRect.y}px`,
      width: `${scaledRect.width}px`,
      height: `${scaledRect.height}px`
    };
  }

  /**
   * 计算文本对齐方式
   */
  calculateTextAlignment(align: 'left' | 'center' | 'right'): {
    textAlign: 'left' | 'center' | 'right';
    justifyContent: 'flex-start' | 'center' | 'flex-end';
  } {
    const alignmentMap = {
      left: { textAlign: 'left' as const, justifyContent: 'flex-start' as const },
      center: { textAlign: 'center' as const, justifyContent: 'center' as const },
      right: { textAlign: 'right' as const, justifyContent: 'flex-end' as const }
    };

    return alignmentMap[align];
  }

  /**
   * 计算字体大小（应用缩放）
   */
  calculateFontSize(originalSize: number): number {
    return originalSize * this.scaleFactor;
  }
}

/**
 * 调试工具
 */
export class CoordinateDebugger {
  /**
   * 生成坐标调试信息
   */
  static generateDebugInfo(
    pdfCoords: Rectangle,
    previewCoords: Rectangle,
    scaleFactor: number
  ): string {
    return `PDF: (${pdfCoords.x}, ${pdfCoords.y}) ${pdfCoords.width}×${pdfCoords.height} → ` +
           `预览: (${previewCoords.x.toFixed(1)}, ${previewCoords.y.toFixed(1)}) ` +
           `${previewCoords.width.toFixed(1)}×${previewCoords.height.toFixed(1)} ` +
           `(缩放: ${(scaleFactor * 100).toFixed(1)}%)`;
  }

  /**
   * 验证坐标转换的正确性
   */
  static validateConversion(
    original: Rectangle,
    converted: Rectangle,
    coordinateSystem: CoordinateSystem
  ): boolean {
    const backConverted = coordinateSystem.previewRectToPdf(converted);
    
    const tolerance = 0.1; // 允许的误差范围
    
    return (
      Math.abs(original.x - backConverted.x) < tolerance &&
      Math.abs(original.y - backConverted.y) < tolerance &&
      Math.abs(original.width - backConverted.width) < tolerance &&
      Math.abs(original.height - backConverted.height) < tolerance
    );
  }
}
