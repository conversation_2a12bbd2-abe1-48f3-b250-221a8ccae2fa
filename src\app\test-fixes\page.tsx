'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import CertificatePreview from '@/components/certificate/CertificatePreview';
import { CertificateData, CertificateTemplate } from '@/types/certificate';
import { CERTIFICATE_TEMPLATES } from '@/lib/certificate-templates';
import { generateCertificatePDF } from '@/lib/pdf-generator';
import { useToast } from '@/hooks/use-toast';
import { Download, Eye, CheckCircle, AlertCircle } from 'lucide-react';
import PixelMeasurementTool from './components/PixelMeasurementTool';
import ComprehensiveTestReport from './components/ComprehensiveTestReport';

export default function TestFixesPage() {
  const { toast } = useToast();
  
  // 获取completion-template-1模板
  const template = CERTIFICATE_TEMPLATES.find(t => t.id === 'completion-template-1');
  
  const [formData, setFormData] = useState<CertificateData>({
    templateId: 'completion-template-1',
    recipientName: '张三',
    date: '2024年1月15日',
    signature: '李老师',
    details: '恭喜您成功完成了《Web开发基础课程》的学习，表现优异，特此颁发此证书以资鼓励。',
  });

  const [showCoordinates, setShowCoordinates] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [debugInfo, setDebugInfo] = useState<string[]>([]);
  const [coordinateAnalysis, setCoordinateAnalysis] = useState<any>(null);
  const [scaleFactor, setScaleFactor] = useState<number>(0.53);

  const handleGeneratePDF = async () => {
    if (!template) {
      toast({
        title: "错误",
        description: "模板未找到",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    setDebugInfo([]); // 清空之前的调试信息

    // 捕获控制台日志
    const originalLog = console.log;
    const originalWarn = console.warn;
    const originalError = console.error;
    const logs: string[] = [];

    console.log = (...args) => {
      logs.push(`[LOG] ${args.join(' ')}`);
      originalLog(...args);
    };
    console.warn = (...args) => {
      logs.push(`[WARN] ${args.join(' ')}`);
      originalWarn(...args);
    };
    console.error = (...args) => {
      logs.push(`[ERROR] ${args.join(' ')}`);
      originalError(...args);
    };

    try {
      console.log('🚀 开始生成PDF...');
      console.log('📋 模板信息:', {
        id: template.id,
        orientation: template.orientation,
        坐标: {
          name: `(${template.layout.name.x}, ${template.layout.name.y})`,
          details: `(${template.layout.details.x}, ${template.layout.details.y})`,
          date: `(${template.layout.date.x}, ${template.layout.date.y})`,
          signature: `(${template.layout.signature.x}, ${template.layout.signature.y})`
        }
      });

      await generateCertificatePDF(template, formData);

      console.log('✅ PDF生成完成');
      toast({
        title: "成功",
        description: "PDF已生成并下载",
        variant: "default",
      });
    } catch (error) {
      console.error('💥 PDF生成错误:', error);
      toast({
        title: "错误",
        description: "PDF生成失败",
        variant: "destructive",
      });
    } finally {
      // 恢复原始控制台方法
      console.log = originalLog;
      console.warn = originalWarn;
      console.error = originalError;

      // 保存调试信息
      setDebugInfo(logs);
      setIsGenerating(false);
    }
  };

  // 坐标分析功能
  const analyzeCoordinates = () => {
    if (!template) return;

    const pdfWidth = 595;
    const pdfHeight = 842;
    const currentScaleFactor = scaleFactor;

    const analysis = {
      模板信息: {
        ID: template.id,
        方向: template.orientation,
        PDF尺寸: `${pdfWidth}x${pdfHeight}`,
        当前缩放因子: currentScaleFactor
      },
      字段分析: {
        姓名: {
          模板坐标: `(${template.layout.name.x}, ${template.layout.name.y})`,
          预览坐标: `(${(template.layout.name.x * currentScaleFactor).toFixed(1)}, ${(template.layout.name.y * currentScaleFactor).toFixed(1)})`,
          PDF坐标: `(${template.layout.name.x}, ${(pdfHeight - template.layout.name.y).toFixed(1)})`,
          尺寸: `${template.layout.name.width}x${template.layout.name.height}`,
          预览尺寸: `${(template.layout.name.width * currentScaleFactor).toFixed(1)}x${(template.layout.name.height * currentScaleFactor).toFixed(1)}`,
          字体: template.layout.name.fontFamily,
          大小: template.layout.name.fontSize,
          预览字体大小: (template.layout.name.fontSize * currentScaleFactor).toFixed(1)
        },
        详情: {
          模板坐标: `(${template.layout.details.x}, ${template.layout.details.y})`,
          预览坐标: `(${(template.layout.details.x * currentScaleFactor).toFixed(1)}, ${(template.layout.details.y * currentScaleFactor).toFixed(1)})`,
          PDF坐标: `(${template.layout.details.x}, ${(pdfHeight - template.layout.details.y).toFixed(1)})`,
          尺寸: `${template.layout.details.width}x${template.layout.details.height}`,
          预览尺寸: `${(template.layout.details.width * currentScaleFactor).toFixed(1)}x${(template.layout.details.height * currentScaleFactor).toFixed(1)}`,
          字体: template.layout.details.fontFamily,
          大小: template.layout.details.fontSize,
          预览字体大小: (template.layout.details.fontSize * currentScaleFactor).toFixed(1)
        },
        日期: {
          模板坐标: `(${template.layout.date.x}, ${template.layout.date.y})`,
          预览坐标: `(${(template.layout.date.x * currentScaleFactor).toFixed(1)}, ${(template.layout.date.y * currentScaleFactor).toFixed(1)})`,
          PDF坐标: `(${template.layout.date.x}, ${(pdfHeight - template.layout.date.y).toFixed(1)})`,
          尺寸: `${template.layout.date.width}x${template.layout.date.height}`,
          预览尺寸: `${(template.layout.date.width * currentScaleFactor).toFixed(1)}x${(template.layout.date.height * currentScaleFactor).toFixed(1)}`,
          字体: template.layout.date.fontFamily,
          大小: template.layout.date.fontSize,
          预览字体大小: (template.layout.date.fontSize * currentScaleFactor).toFixed(1)
        },
        签名: {
          模板坐标: `(${template.layout.signature.x}, ${template.layout.signature.y})`,
          预览坐标: `(${(template.layout.signature.x * currentScaleFactor).toFixed(1)}, ${(template.layout.signature.y * currentScaleFactor).toFixed(1)})`,
          PDF坐标: `(${template.layout.signature.x}, ${(pdfHeight - template.layout.signature.y).toFixed(1)})`,
          尺寸: `${template.layout.signature.width}x${template.layout.signature.height}`,
          预览尺寸: `${(template.layout.signature.width * currentScaleFactor).toFixed(1)}x${(template.layout.signature.height * currentScaleFactor).toFixed(1)}`,
          字体: template.layout.signature.fontFamily,
          大小: template.layout.signature.fontSize,
          预览字体大小: (template.layout.signature.fontSize * currentScaleFactor).toFixed(1)
        }
      },
      缩放分析: {
        当前缩放因子: currentScaleFactor,
        预览容器理论尺寸: `${(pdfWidth * currentScaleFactor).toFixed(1)}x${(pdfHeight * currentScaleFactor).toFixed(1)}`,
        建议缩放因子: {
          '0.50': '较小预览',
          '0.53': '当前设置',
          '0.55': '稍大预览',
          '0.60': '较大预览'
        }
      }
    };

    setCoordinateAnalysis(analysis);
    console.log('📐 坐标分析结果:', analysis);
  };

  if (!template) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="p-8 text-center">
            <AlertCircle className="mx-auto h-12 w-12 text-red-500 mb-4" />
            <h2 className="text-xl font-semibold mb-2">模板未找到</h2>
            <p className="text-gray-600">completion-template-1 模板不存在</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">证书生成器修复测试</h1>
        <p className="text-gray-600 mb-4">
          此页面用于测试预览图与PDF输出的一致性修复效果
        </p>
        
        {/* 修复状态指示器 */}
        <div className="flex flex-wrap gap-2 mb-6">
          <Badge variant="outline" className="flex items-center gap-1">
            <CheckCircle className="h-3 w-3 text-green-500" />
            绿色横线已移除
          </Badge>
          <Badge variant="outline" className="flex items-center gap-1">
            <CheckCircle className="h-3 w-3 text-green-500" />
            坐标系统已统一
          </Badge>
          <Badge variant="outline" className="flex items-center gap-1">
            <CheckCircle className="h-3 w-3 text-green-500" />
            字体处理已优化
          </Badge>
        </div>
      </div>

      <div className="grid lg:grid-cols-2 gap-8">
        {/* 左侧：控制面板 */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>测试控制</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">收件人姓名</label>
                <input
                  type="text"
                  value={formData.recipientName}
                  onChange={(e) => setFormData({...formData, recipientName: e.target.value})}
                  className="w-full px-3 py-2 border rounded-md"
                />
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">详细信息</label>
                <textarea
                  value={formData.details}
                  onChange={(e) => setFormData({...formData, details: e.target.value})}
                  className="w-full px-3 py-2 border rounded-md h-20"
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">日期</label>
                  <input
                    type="text"
                    value={formData.date}
                    onChange={(e) => setFormData({...formData, date: e.target.value})}
                    className="w-full px-3 py-2 border rounded-md"
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">签名</label>
                  <input
                    type="text"
                    value={formData.signature}
                    onChange={(e) => setFormData({...formData, signature: e.target.value})}
                    className="w-full px-3 py-2 border rounded-md"
                  />
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="showCoordinates"
                  checked={showCoordinates}
                  onChange={(e) => setShowCoordinates(e.target.checked)}
                  className="rounded"
                />
                <label htmlFor="showCoordinates" className="text-sm font-medium">
                  显示坐标调试
                </label>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">缩放因子调整</label>
                <div className="flex items-center space-x-2">
                  <input
                    type="range"
                    min="0.4"
                    max="0.7"
                    step="0.01"
                    value={scaleFactor}
                    onChange={(e) => setScaleFactor(parseFloat(e.target.value))}
                    className="flex-1"
                  />
                  <span className="text-sm w-12">{scaleFactor.toFixed(2)}</span>
                </div>
                <div className="text-xs text-gray-600">
                  调整预览图的缩放比例以匹配PDF
                </div>
              </div>

              <div className="flex gap-2">
                <Button
                  onClick={analyzeCoordinates}
                  variant="outline"
                  size="sm"
                  className="flex-1"
                >
                  📐 分析坐标
                </Button>
              </div>
              
              <div className="flex gap-2">
                <Button
                  onClick={handleGeneratePDF}
                  disabled={isGenerating}
                  className="flex-1"
                >
                  {isGenerating ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      生成中...
                    </>
                  ) : (
                    <>
                      <Download className="h-4 w-4 mr-2" />
                      生成PDF
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 测试说明 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                测试说明
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
              <div>
                <h4 className="font-medium mb-1">1. 位置一致性测试</h4>
                <p className="text-gray-600">对比预览图和生成的PDF中文本位置是否一致</p>
              </div>
              <div>
                <h4 className="font-medium mb-1">2. 字体效果测试</h4>
                <p className="text-gray-600">验证Dancing Script字体在PDF中的显示效果</p>
              </div>
              <div>
                <h4 className="font-medium mb-1">3. 绿色横线检查</h4>
                <p className="text-gray-600">确认PDF中不再出现不需要的绿色横线</p>
              </div>
              <div>
                <h4 className="font-medium mb-1">4. 坐标调试</h4>
                <p className="text-gray-600">勾选"显示坐标调试"查看各字段的边界框</p>
              </div>
            </CardContent>
          </Card>

          {/* 坐标分析面板 */}
          {coordinateAnalysis && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  📐 坐标精度分析
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCoordinateAnalysis(null)}
                  >
                    关闭
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <h4 className="font-medium text-blue-600 mb-2">模板信息</h4>
                      <div className="space-y-1">
                        <div>ID: {coordinateAnalysis.模板信息.ID}</div>
                        <div>方向: {coordinateAnalysis.模板信息.方向}</div>
                        <div>PDF尺寸: {coordinateAnalysis.模板信息.PDF尺寸}</div>
                        <div>缩放因子: {coordinateAnalysis.模板信息.当前缩放因子}</div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium text-green-600 mb-2">缩放分析</h4>
                      <div className="space-y-1">
                        <div>预览容器: {coordinateAnalysis.缩放分析.预览容器理论尺寸}</div>
                        <div className="text-xs text-gray-600">
                          建议缩放因子:
                          {Object.entries(coordinateAnalysis.缩放分析.建议缩放因子).map(([factor, desc]) => (
                            <div key={factor} className={`ml-2 ${factor === coordinateAnalysis.缩放分析.当前缩放因子.toString() ? 'font-bold text-blue-600' : ''}`}>
                              {factor}: {desc}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-purple-600 mb-2">字段坐标对比</h4>
                    <div className="overflow-x-auto">
                      <table className="w-full text-xs border-collapse">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left p-2">字段</th>
                            <th className="text-left p-2">模板坐标</th>
                            <th className="text-left p-2">预览坐标</th>
                            <th className="text-left p-2">PDF坐标</th>
                            <th className="text-left p-2">字体大小</th>
                          </tr>
                        </thead>
                        <tbody>
                          {Object.entries(coordinateAnalysis.字段分析).map(([field, data]: [string, any]) => (
                            <tr key={field} className="border-b">
                              <td className="p-2 font-medium">{field}</td>
                              <td className="p-2">{data.模板坐标}</td>
                              <td className="p-2 text-blue-600">{data.预览坐标}</td>
                              <td className="p-2 text-green-600">{data.PDF坐标}</td>
                              <td className="p-2">{data.大小} → {data.预览字体大小}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 调试信息面板 */}
          {debugInfo.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  🐛 调试信息
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setDebugInfo([])}
                  >
                    清空
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-900 text-green-400 p-4 rounded-lg max-h-60 overflow-y-auto font-mono text-xs">
                  {debugInfo.map((log, index) => (
                    <div key={index} className={`mb-1 ${
                      log.includes('[ERROR]') ? 'text-red-400' :
                      log.includes('[WARN]') ? 'text-yellow-400' :
                      log.includes('✅') ? 'text-green-400' :
                      log.includes('❌') ? 'text-red-400' :
                      log.includes('💃') ? 'text-pink-400' :
                      'text-gray-300'
                    }`}>
                      {log}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* 右侧：预览 */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>实时预览</CardTitle>
            </CardHeader>
            <CardContent>
              <CertificatePreview
                template={template}
                formData={formData}
                showCoordinates={showCoordinates}
                customScaleFactor={scaleFactor}
              />
            </CardContent>
          </Card>

          {/* 坐标信息面板 */}
          <Card>
            <CardHeader>
              <CardTitle>📐 坐标信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <h4 className="font-medium text-red-600 mb-2">姓名字段</h4>
                  <div className="space-y-1">
                    <div>X: {template.layout.name.x}px</div>
                    <div>Y: {template.layout.name.y}px</div>
                    <div>宽: {template.layout.name.width}px</div>
                    <div>高: {template.layout.name.height}px</div>
                    <div>对齐: {template.layout.name.align}</div>
                    <div>字体: {template.layout.name.fontFamily}</div>
                    <div>大小: {template.layout.name.fontSize}px</div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-blue-600 mb-2">详情字段</h4>
                  <div className="space-y-1">
                    <div>X: {template.layout.details.x}px</div>
                    <div>Y: {template.layout.details.y}px</div>
                    <div>宽: {template.layout.details.width}px</div>
                    <div>高: {template.layout.details.height}px</div>
                    <div>对齐: {template.layout.details.align}</div>
                    <div>字体: {template.layout.details.fontFamily}</div>
                    <div>大小: {template.layout.details.fontSize}px</div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-green-600 mb-2">日期字段</h4>
                  <div className="space-y-1">
                    <div>X: {template.layout.date.x}px</div>
                    <div>Y: {template.layout.date.y}px</div>
                    <div>宽: {template.layout.date.width}px</div>
                    <div>高: {template.layout.date.height}px</div>
                    <div>对齐: {template.layout.date.align}</div>
                    <div>字体: {template.layout.date.fontFamily}</div>
                    <div>大小: {template.layout.date.fontSize}px</div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-purple-600 mb-2">签名字段</h4>
                  <div className="space-y-1">
                    <div>X: {template.layout.signature.x}px</div>
                    <div>Y: {template.layout.signature.y}px</div>
                    <div>宽: {template.layout.signature.width}px</div>
                    <div>高: {template.layout.signature.height}px</div>
                    <div>对齐: {template.layout.signature.align}</div>
                    <div>字体: {template.layout.signature.fontFamily}</div>
                    <div>大小: {template.layout.signature.fontSize}px</div>
                  </div>
                </div>
              </div>

              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-800 mb-2">坐标系统说明</h4>
                <div className="text-sm text-blue-700 space-y-1">
                  <div>• 预览组件：Y轴从顶部开始，向下为正</div>
                  <div>• PDF生成器：Y轴从底部开始，向上为正</div>
                  <div>• 模板坐标：从顶部开始计算</div>
                  <div>• 转换公式：PDF_Y = height - template_Y</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* 高级测试工具区域 */}
      <div className="mt-8 space-y-6">
        <h2 className="text-2xl font-bold">🔬 高级测试工具</h2>

        <div className="grid lg:grid-cols-2 gap-6">
          {/* 像素测量工具 */}
          <PixelMeasurementTool
            title="预览图像素测量"
            description="点击预览图上的文字位置，测量精确的像素坐标。用于对比预览图和PDF中文字的实际位置差异。"
          />

          <PixelMeasurementTool
            title="PDF像素测量"
            description="在PDF查看器中截图后，使用此工具测量PDF中文字的像素位置。对比预览图测量结果分析位置偏差。"
          />
        </div>

        {/* 综合测试报告 */}
        <ComprehensiveTestReport
          debugInfo={debugInfo}
          coordinateAnalysis={coordinateAnalysis}
          scaleFactor={scaleFactor}
        />
      </div>
    </div>
  );
}
