'use client';

/**
 * 字体修复验证脚本
 * 用于快速验证Dancing Script字体和渲染一致性修复效果
 */

import { PDFDocument } from 'pdf-lib';
import { UnifiedFontManager } from './unified-font-manager';
import { RenderingConsistencyManager } from './rendering-consistency-manager';
import { CERTIFICATE_TEMPLATES } from './certificate-templates';

export interface QuickTestResult {
  dancingScriptTest: {
    success: boolean;
    fontName?: string;
    fallbackUsed: boolean;
    error?: string;
  };
  renderingConsistency: {
    configValid: boolean;
    scaleFactor: number;
    containerDimensions: { width: number; height: number };
    pdfDimensions: { width: number; height: number };
  };
  overallStatus: 'success' | 'partial' | 'failed';
  recommendations: string[];
}

/**
 * 快速测试字体修复效果
 */
export async function quickTestFontFix(): Promise<QuickTestResult> {
  console.log('🧪 开始快速测试字体修复效果...');
  
  const result: QuickTestResult = {
    dancingScriptTest: {
      success: false,
      fallbackUsed: false
    },
    renderingConsistency: {
      configValid: false,
      scaleFactor: 0,
      containerDimensions: { width: 0, height: 0 },
      pdfDimensions: { width: 0, height: 0 }
    },
    overallStatus: 'failed',
    recommendations: []
  };

  try {
    // 1. 测试Dancing Script字体加载
    console.log('🔤 测试Dancing Script字体加载...');
    const fontManager = UnifiedFontManager.getInstance();
    const pdfDoc = await PDFDocument.create();
    
    const fontResult = await fontManager.loadFont(pdfDoc, 'Dancing Script', 400);
    result.dancingScriptTest = {
      success: fontResult.success,
      fontName: fontResult.font?.name,
      fallbackUsed: fontResult.fallbackUsed || false,
      error: fontResult.error
    };

    if (fontResult.success) {
      console.log('✅ Dancing Script字体加载成功:', fontResult.font?.name);
    } else {
      console.log('❌ Dancing Script字体加载失败:', fontResult.error);
      result.recommendations.push('检查网络连接和Google Fonts可用性');
    }

    if (fontResult.fallbackUsed) {
      console.log('⚠️ 使用了后备字体');
      result.recommendations.push('优化字体加载策略，减少后备字体使用');
    }

    // 2. 测试渲染一致性配置
    console.log('📐 测试渲染一致性配置...');
    const template = CERTIFICATE_TEMPLATES.find(t => t.id === 'completion-template-1');
    
    if (template) {
      const renderingManager = new RenderingConsistencyManager(template);
      const config = renderingManager.getRenderingConfig();
      
      result.renderingConsistency = {
        configValid: config.scaleFactor > 0 && config.scaleFactor <= 1,
        scaleFactor: config.scaleFactor,
        containerDimensions: {
          width: config.containerWidth,
          height: config.containerHeight
        },
        pdfDimensions: {
          width: config.pdfWidth,
          height: config.pdfHeight
        }
      };

      if (result.renderingConsistency.configValid) {
        console.log('✅ 渲染一致性配置有效:', config);
      } else {
        console.log('❌ 渲染一致性配置异常:', config);
        result.recommendations.push('检查模板配置和容器尺寸设置');
      }
    } else {
      console.log('❌ 找不到completion-template-1模板');
      result.recommendations.push('确保completion-template-1模板配置正确');
    }

    // 3. 计算总体状态
    const fontOk = result.dancingScriptTest.success && !result.dancingScriptTest.fallbackUsed;
    const renderingOk = result.renderingConsistency.configValid;

    if (fontOk && renderingOk) {
      result.overallStatus = 'success';
      console.log('🎉 所有测试通过！字体修复效果良好');
    } else if (result.dancingScriptTest.success || result.renderingConsistency.configValid) {
      result.overallStatus = 'partial';
      console.log('⚠️ 部分测试通过，需要进一步优化');
    } else {
      result.overallStatus = 'failed';
      console.log('❌ 测试失败，需要检查修复实现');
      result.recommendations.push('重新检查字体管理器和渲染一致性管理器的实现');
    }

    // 清理资源
    fontManager.clearCache();

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    result.overallStatus = 'failed';
    result.recommendations.push(`修复测试错误: ${error instanceof Error ? error.message : String(error)}`);
  }

  return result;
}

/**
 * 生成测试报告
 */
export function generateTestReport(result: QuickTestResult): string {
  const statusEmoji = {
    success: '✅',
    partial: '⚠️',
    failed: '❌'
  };

  const report = [
    '# 字体修复快速测试报告',
    '',
    `## 总体状态: ${statusEmoji[result.overallStatus]} ${result.overallStatus.toUpperCase()}`,
    '',
    '## Dancing Script字体测试',
    `- 加载状态: ${result.dancingScriptTest.success ? '✅ 成功' : '❌ 失败'}`,
    `- 字体名称: ${result.dancingScriptTest.fontName || '未知'}`,
    `- 后备字体: ${result.dancingScriptTest.fallbackUsed ? '⚠️ 是' : '✅ 否'}`,
    result.dancingScriptTest.error ? `- 错误信息: ${result.dancingScriptTest.error}` : '',
    '',
    '## 渲染一致性测试',
    `- 配置有效性: ${result.renderingConsistency.configValid ? '✅ 有效' : '❌ 无效'}`,
    `- 缩放因子: ${result.renderingConsistency.scaleFactor.toFixed(3)}`,
    `- 容器尺寸: ${result.renderingConsistency.containerDimensions.width}x${result.renderingConsistency.containerDimensions.height}`,
    `- PDF尺寸: ${result.renderingConsistency.pdfDimensions.width}x${result.renderingConsistency.pdfDimensions.height}`,
    '',
    result.recommendations.length > 0 ? '## 改进建议' : '',
    ...result.recommendations.map(rec => `- ${rec}`),
    '',
    `## 测试时间: ${new Date().toLocaleString()}`
  ].filter(line => line !== ''); // 移除空字符串

  return report.join('\n');
}

/**
 * 在控制台输出彩色测试结果
 */
export function logTestResult(result: QuickTestResult): void {
  const styles = {
    success: 'color: #10b981; font-weight: bold;',
    warning: 'color: #f59e0b; font-weight: bold;',
    error: 'color: #ef4444; font-weight: bold;',
    info: 'color: #3b82f6;'
  };

  console.log('%c🧪 字体修复测试结果', styles.info);
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

  // Dancing Script测试结果
  if (result.dancingScriptTest.success) {
    if (result.dancingScriptTest.fallbackUsed) {
      console.log('%c⚠️ Dancing Script: 加载成功但使用后备字体', styles.warning);
    } else {
      console.log('%c✅ Dancing Script: 加载成功', styles.success);
    }
  } else {
    console.log('%c❌ Dancing Script: 加载失败', styles.error);
  }

  // 渲染一致性测试结果
  if (result.renderingConsistency.configValid) {
    console.log('%c✅ 渲染一致性: 配置有效', styles.success);
  } else {
    console.log('%c❌ 渲染一致性: 配置异常', styles.error);
  }

  // 总体状态
  const statusStyle = result.overallStatus === 'success' ? styles.success :
                     result.overallStatus === 'partial' ? styles.warning : styles.error;
  console.log(`%c🎯 总体状态: ${result.overallStatus.toUpperCase()}`, statusStyle);

  // 建议
  if (result.recommendations.length > 0) {
    console.log('%c💡 改进建议:', styles.info);
    result.recommendations.forEach(rec => {
      console.log(`   • ${rec}`);
    });
  }

  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
}

/**
 * 便捷的测试函数 - 运行测试并输出结果
 */
export async function runQuickTest(): Promise<QuickTestResult> {
  const result = await quickTestFontFix();
  logTestResult(result);
  return result;
}

// 导出给浏览器控制台使用的全局函数
if (typeof window !== 'undefined') {
  (window as any).testFontFix = runQuickTest;
  (window as any).generateFontTestReport = (result: QuickTestResult) => {
    console.log(generateTestReport(result));
  };
}
