'use client';

import React, { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface MeasurementPoint {
  x: number;
  y: number;
  label: string;
  timestamp: number;
}

interface PixelMeasurementToolProps {
  title: string;
  description: string;
}

export default function PixelMeasurementTool({ title, description }: PixelMeasurementToolProps) {
  const [measurements, setMeasurements] = useState<MeasurementPoint[]>([]);
  const [isActive, setIsActive] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleClick = useCallback((event: React.MouseEvent<HTMLDivElement>) => {
    if (!isActive || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    const newMeasurement: MeasurementPoint = {
      x: Math.round(x),
      y: Math.round(y),
      label: `点${measurements.length + 1}`,
      timestamp: Date.now()
    };

    setMeasurements(prev => [...prev, newMeasurement]);
  }, [isActive, measurements.length]);

  const clearMeasurements = () => {
    setMeasurements([]);
  };

  const toggleActive = () => {
    setIsActive(!isActive);
  };

  const calculateDistance = (point1: MeasurementPoint, point2: MeasurementPoint) => {
    const dx = point2.x - point1.x;
    const dy = point2.y - point1.y;
    return Math.sqrt(dx * dx + dy * dy);
  };

  const exportMeasurements = () => {
    const data = {
      title,
      description,
      timestamp: new Date().toISOString(),
      measurements,
      analysis: measurements.length >= 2 ? {
        distances: measurements.slice(1).map((point, index) => ({
          from: measurements[index].label,
          to: point.label,
          distance: calculateDistance(measurements[index], point).toFixed(2),
          deltaX: point.x - measurements[index].x,
          deltaY: point.y - measurements[index].y
        }))
      } : null
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `measurements-${title.replace(/\s+/g, '-')}-${Date.now()}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>📏 {title}</span>
          <div className="flex gap-2">
            <Button
              variant={isActive ? "destructive" : "default"}
              size="sm"
              onClick={toggleActive}
            >
              {isActive ? '停止测量' : '开始测量'}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={clearMeasurements}
              disabled={measurements.length === 0}
            >
              清空
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={exportMeasurements}
              disabled={measurements.length === 0}
            >
              导出
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <p className="text-sm text-gray-600">{description}</p>
          
          {isActive && (
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-sm text-blue-800">
                🎯 测量模式已激活 - 点击图像上的任意位置进行测量
              </p>
            </div>
          )}

          <div
            ref={containerRef}
            className={`relative border-2 border-dashed ${
              isActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
            } rounded-lg p-4 min-h-[200px] cursor-${isActive ? 'crosshair' : 'default'}`}
            onClick={handleClick}
          >
            <div className="text-center text-gray-500 text-sm">
              {isActive ? '点击此区域进行像素测量' : '点击"开始测量"激活测量工具'}
            </div>

            {/* 渲染测量点 */}
            {measurements.map((point, index) => (
              <div
                key={point.timestamp}
                className="absolute w-3 h-3 bg-red-500 border-2 border-white rounded-full shadow-lg transform -translate-x-1/2 -translate-y-1/2"
                style={{ left: point.x, top: point.y }}
              >
                <div className="absolute top-4 left-1/2 transform -translate-x-1/2 bg-red-500 text-white text-xs px-1 py-0.5 rounded whitespace-nowrap">
                  {point.label}: ({point.x}, {point.y})
                </div>
              </div>
            ))}

            {/* 渲染连接线 */}
            {measurements.length >= 2 && (
              <svg className="absolute inset-0 w-full h-full pointer-events-none">
                {measurements.slice(1).map((point, index) => {
                  const prevPoint = measurements[index];
                  return (
                    <line
                      key={`line-${index}`}
                      x1={prevPoint.x}
                      y1={prevPoint.y}
                      x2={point.x}
                      y2={point.y}
                      stroke="#ef4444"
                      strokeWidth="2"
                      strokeDasharray="5,5"
                    />
                  );
                })}
              </svg>
            )}
          </div>

          {/* 测量结果 */}
          {measurements.length > 0 && (
            <div className="space-y-3">
              <h4 className="font-medium">测量结果</h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {measurements.map((point, index) => (
                  <div key={point.timestamp} className="flex items-center gap-2">
                    <Badge variant="outline">{point.label}</Badge>
                    <span className="text-sm">({point.x}, {point.y})</span>
                  </div>
                ))}
              </div>

              {measurements.length >= 2 && (
                <div className="space-y-2">
                  <h5 className="font-medium text-sm">距离分析</h5>
                  {measurements.slice(1).map((point, index) => {
                    const prevPoint = measurements[index];
                    const distance = calculateDistance(prevPoint, point);
                    const deltaX = point.x - prevPoint.x;
                    const deltaY = point.y - prevPoint.y;
                    
                    return (
                      <div key={`distance-${index}`} className="text-sm bg-gray-50 p-2 rounded">
                        <div className="font-medium">
                          {prevPoint.label} → {point.label}
                        </div>
                        <div className="text-gray-600">
                          距离: {distance.toFixed(2)}px | 
                          ΔX: {deltaX}px | 
                          ΔY: {deltaY}px
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
